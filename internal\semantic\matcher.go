package semantic

import (
	"faq-system/internal/mysql"
	"faq-system/internal/vectorstore"
	"log"
	"sort"
	"strings"
)

// MatchResult 匹配结果
type MatchResult struct {
	FAQ          mysql.FAQ
	Score        float32
	MatchType    string
	QuestionType QuestionType
	Explanation  string
}

// SmartMatcher 智能匹配器
type SmartMatcher struct {
	classifier *QuestionClassifier
	faqs       []mysql.FAQ
}

// NewSmartMatcher 创建智能匹配器
func NewSmartMatcher(faqs []mysql.FAQ) *SmartMatcher {
	return &SmartMatcher{
		classifier: NewQuestionClassifier(),
		faqs:       faqs,
	}
}

// FindBestMatch 找到最佳匹配
func (sm *SmartMatcher) FindBestMatch(question string, vectorResults []vectorstore.SearchResult) *MatchResult {
	questionType := sm.classifier.ClassifyQuestion(question)

	// 对向量搜索结果进行智能重排序
	candidates := sm.rankCandidates(question, questionType, vectorResults)

	if len(candidates) == 0 {
		return nil
	}

	return candidates[0]
}

// rankCandidates 对候选答案进行智能排序
func (sm *SmartMatcher) rankCandidates(question string, questionType QuestionType, vectorResults []vectorstore.SearchResult) []*MatchResult {
	var candidates []*MatchResult

	for _, result := range vectorResults {
		faq := sm.getFAQByID(result.ID)
		if faq == nil {
			continue
		}

		// 计算综合得分
		score := sm.calculateComprehensiveScore(question, questionType, *faq, result.Score)

		matchResult := &MatchResult{
			FAQ:          *faq,
			Score:        score,
			QuestionType: questionType,
		}

		// 设置匹配类型和解释
		sm.setMatchTypeAndExplanation(matchResult, question, questionType)

		candidates = append(candidates, matchResult)
	}

	// 按得分排序
	sort.Slice(candidates, func(i, j int) bool {
		return candidates[i].Score > candidates[j].Score
	})

	return candidates
}

// calculateComprehensiveScore 计算综合得分
func (sm *SmartMatcher) calculateComprehensiveScore(question string, questionType QuestionType, faq mysql.FAQ, vectorScore float32) float32 {
	score := vectorScore

	// 🛡️ 首先进行严格的主题检查 - 如果主题冲突，直接拒绝
	if sm.hasTopicConflict(question, faq) {
		return 0.0 // 主题冲突，完全拒绝匹配
	}

	// 然后检查语义相关性 - 如果问题和FAQ完全不相关，直接返回极低得分
	if !sm.isSemanticallySimilar(question, faq) {
		return 0.05 // 直接返回极低得分，避免不相关内容被匹配
	}

	// 基于问题类型的加权
	typeBonus := sm.getTypeMatchBonus(question, questionType, faq)
	score += typeBonus

	// 基于关键词匹配的加权
	keywordBonus := sm.getKeywordMatchBonus(question, faq)
	score += keywordBonus

	// 基于问题长度相似性的加权
	lengthBonus := sm.getLengthSimilarityBonus(question, faq.Question)
	score += lengthBonus

	// 确保得分不超过1.0
	if score > 1.0 {
		score = 1.0
	}

	return score
}

// getTypeMatchBonus 获取类型匹配加成
func (sm *SmartMatcher) getTypeMatchBonus(question string, questionType QuestionType, faq mysql.FAQ) float32 {
	faqType := sm.classifier.ClassifyQuestion(faq.Question)

	// 如果问题类型匹配，给予加成
	if questionType == faqType && questionType != Unknown {
		// 配置类问题给予更高的匹配加成
		if questionType == Configuration {
			return 0.4
		}
		return 0.2
	}

	// 特殊情况处理
	switch questionType {
	case Configuration:
		if sm.isConfigurationRelated(faq) {
			// 配置类问题的相关性检查给予更高权重
			return 0.5
		}
	case Installation:
		if sm.isInstallationRelated(faq) {
			return 0.3
		}
	case HowTo:
		if sm.isHowToRelated(faq) {
			return 0.2
		}
	case WhatIs:
		if sm.isDefinitionRelated(faq) {
			return 0.2
		}
	}

	return 0.0
}

// getKeywordMatchBonus 获取关键词匹配加成
func (sm *SmartMatcher) getKeywordMatchBonus(question string, faq mysql.FAQ) float32 {
	questionLower := strings.ToLower(question)
	faqQuestionLower := strings.ToLower(faq.Question)
	faqAnswerLower := strings.ToLower(faq.Answer)

	bonus := float32(0)

	// 提取问题中的关键技术词汇
	techKeywords := sm.extractTechKeywords(questionLower)

	for _, keyword := range techKeywords {
		if strings.Contains(faqQuestionLower, keyword) {
			bonus += 0.1
		}
		if strings.Contains(faqAnswerLower, keyword) {
			bonus += 0.05
		}
	}

	// 特殊处理：配置相关的组合关键词（降低权重）
	if sm.hasConfigurationCombo(questionLower) && sm.hasConfigurationCombo(faqQuestionLower) {
		bonus += 0.15 // 降低配置组合匹配的分数
	}

	// 特殊处理：安装相关的组合关键词（降低权重）
	if sm.hasInstallationCombo(questionLower) && sm.hasInstallationCombo(faqQuestionLower) {
		bonus += 0.15 // 降低安装组合匹配的分数
	}

	return bonus
}

// hasConfigurationCombo 检查是否包含配置相关的组合关键词
func (sm *SmartMatcher) hasConfigurationCombo(text string) bool {
	configCombos := [][]string{
		{"go", "配置"}, {"golang", "配置"}, {"go", "环境"}, {"golang", "环境"},
		{"开发", "环境"}, {"环境", "配置"}, {"开发", "配置"},
		{"java", "配置"}, {"python", "配置"}, {"node", "配置"},
	}

	for _, combo := range configCombos {
		hasAll := true
		for _, word := range combo {
			if !strings.Contains(text, word) {
				hasAll = false
				break
			}
		}
		if hasAll {
			return true
		}
	}
	return false
}

// hasInstallationCombo 检查是否包含安装相关的组合关键词
func (sm *SmartMatcher) hasInstallationCombo(text string) bool {
	installCombos := [][]string{
		{"go", "安装"}, {"golang", "安装"}, {"go", "部署"}, {"golang", "部署"},
		{"安装", "环境"}, {"部署", "环境"}, {"搭建", "环境"},
		{"java", "安装"}, {"python", "安装"}, {"node", "安装"},
	}

	for _, combo := range installCombos {
		hasAll := true
		for _, word := range combo {
			if !strings.Contains(text, word) {
				hasAll = false
				break
			}
		}
		if hasAll {
			return true
		}
	}
	return false
}

// getLengthSimilarityBonus 获取长度相似性加成
func (sm *SmartMatcher) getLengthSimilarityBonus(question string, faqQuestion string) float32 {
	qLen := len(question)
	faqLen := len(faqQuestion)

	if qLen == 0 || faqLen == 0 {
		return 0
	}

	ratio := float32(min(qLen, faqLen)) / float32(max(qLen, faqLen))

	// 长度相似性加成（最大0.1）
	return ratio * 0.1
}

// extractTechKeywords 提取技术关键词
func (sm *SmartMatcher) extractTechKeywords(text string) []string {
	techWords := []string{
		"go", "golang", "mysql", "localai", "embedding", "vector", "rag",
		"数据库", "配置", "安装", "部署", "环境", "开发", "编程",
		"java", "python", "javascript", "docker", "redis", "mongodb",
	}

	var found []string
	for _, word := range techWords {
		if strings.Contains(text, word) {
			found = append(found, word)
		}
	}

	return found
}

// 辅助方法：检查FAQ是否与特定类型相关
func (sm *SmartMatcher) isConfigurationRelated(faq mysql.FAQ) bool {
	text := strings.ToLower(faq.Question + " " + faq.Answer)

	// 强配置关键词（权重更高）
	strongConfigWords := []string{
		"环境配置", "开发环境", "配置文件", "环境变量", "系统配置",
		"environment config", "dev environment", "configuration",
	}

	for _, word := range strongConfigWords {
		if strings.Contains(text, word) {
			return true
		}
	}

	// 一般配置关键词
	configWords := []string{"配置", "设置", "环境", "参数", "config", "setup", "environment"}

	configCount := 0
	for _, word := range configWords {
		if strings.Contains(text, word) {
			configCount++
		}
	}

	// 如果包含多个配置相关词汇，认为是配置相关
	return configCount >= 2
}

func (sm *SmartMatcher) isInstallationRelated(faq mysql.FAQ) bool {
	text := strings.ToLower(faq.Question + " " + faq.Answer)
	installWords := []string{"安装", "部署", "搭建", "install", "deploy", "setup", "download"}

	for _, word := range installWords {
		if strings.Contains(text, word) {
			return true
		}
	}
	return false
}

func (sm *SmartMatcher) isHowToRelated(faq mysql.FAQ) bool {
	text := strings.ToLower(faq.Question + " " + faq.Answer)
	howToWords := []string{"如何", "怎么", "方法", "步骤", "how", "way", "method", "step"}

	for _, word := range howToWords {
		if strings.Contains(text, word) {
			return true
		}
	}
	return false
}

func (sm *SmartMatcher) isDefinitionRelated(faq mysql.FAQ) bool {
	text := strings.ToLower(faq.Question + " " + faq.Answer)
	defWords := []string{"什么是", "定义", "介绍", "概念", "what is", "define", "definition"}

	for _, word := range defWords {
		if strings.Contains(text, word) {
			return true
		}
	}
	return false
}

// isSemanticallySimilar 检查问题和FAQ是否语义相似
func (sm *SmartMatcher) isSemanticallySimilar(question string, faq mysql.FAQ) bool {
	questionLower := strings.ToLower(question)
	faqQuestionLower := strings.ToLower(faq.Question)
	faqAnswerLower := strings.ToLower(faq.Answer)

	// 分类问题类型
	questionType := sm.classifier.ClassifyQuestion(question)
	faqType := sm.classifier.ClassifyQuestion(faq.Question)

	// 提取问题中的核心技术词汇
	questionTechWords := sm.extractTechKeywords(questionLower)
	faqTechWords := sm.extractTechKeywords(faqQuestionLower + " " + faqAnswerLower)

	// 检查技术领域的精确匹配
	if !sm.isSameTechnicalDomain(questionTechWords, faqTechWords, questionLower, faqQuestionLower+" "+faqAnswerLower) {
		return false
	}

	// 检查是否有共同的技术词汇
	hasCommonTech := false
	for _, qWord := range questionTechWords {
		for _, fWord := range faqTechWords {
			if qWord == fWord {
				hasCommonTech = true
				break
			}
		}
		if hasCommonTech {
			break
		}
	}

	// 如果有共同技术词汇，还需要检查问题类型是否匹配
	if hasCommonTech {
		// 特别处理：配置类问题不应该匹配到非配置类FAQ
		if questionType == Configuration {
			// 配置类问题只能匹配配置类FAQ或操作类FAQ
			if faqType != Configuration && faqType != HowTo && faqType != Installation {
				// 对于Unknown类型的FAQ，进行严格的内容检查
				if faqType == Unknown {
					faqContent := strings.ToLower(faq.Question + " " + faq.Answer)

					// 强配置关键词（必须包含这些才认为是配置相关）
					strongConfigWords := []string{"环境配置", "开发环境", "配置文件", "环境变量", "系统配置", "安装配置", "部署配置"}
					hasStrongConfig := false
					for _, word := range strongConfigWords {
						if strings.Contains(faqContent, word) {
							hasStrongConfig = true
							break
						}
					}

					// 如果没有强配置关键词，检查是否有多个配置相关词汇
					if !hasStrongConfig {
						configWords := []string{"配置", "设置", "环境", "安装", "搭建"}
						configCount := 0
						for _, word := range configWords {
							if strings.Contains(faqContent, word) {
								configCount++
							}
						}
						// 需要至少包含3个配置相关词汇才认为是配置相关（提高要求）
						if configCount < 3 {
							return false // FAQ内容与配置关系不够强
						}
					}
				} else {
					// 非Unknown类型且不是配置相关类型，直接返回false
					return false
				}
			}
		}

		// 检查其他不兼容的类型组合
		if questionType != Unknown && faqType != Unknown && questionType != faqType {
			incompatiblePairs := [][]QuestionType{
				{Installation, WhatIs},      // 安装 vs 定义
				{Troubleshooting, WhatIs},   // 故障排除 vs 定义
				{Configuration, Comparison}, // 配置 vs 比较
				{Installation, Comparison},  // 安装 vs 比较
			}

			for _, pair := range incompatiblePairs {
				if (questionType == pair[0] && faqType == pair[1]) ||
					(questionType == pair[1] && faqType == pair[0]) {
					return false // 不兼容的类型组合
				}
			}
		}
		return true
	}

	// 如果没有共同的技术词汇，检查是否是通用问题
	generalPatterns := []string{"什么是", "什么叫", "介绍", "定义"}
	questionIsGeneral := false
	faqIsGeneral := false

	for _, pattern := range generalPatterns {
		if strings.Contains(questionLower, pattern) {
			questionIsGeneral = true
		}
		if strings.Contains(faqQuestionLower, pattern) {
			faqIsGeneral = true
		}
	}

	// 如果都是通用问题但技术领域不同，认为不相似
	if questionIsGeneral && faqIsGeneral && len(questionTechWords) > 0 && len(faqTechWords) > 0 {
		return false
	}

	return len(questionTechWords) == 0 && len(faqTechWords) == 0
}

// isSameTechnicalDomain 检查是否属于同一技术领域
func (sm *SmartMatcher) isSameTechnicalDomain(questionTechWords, faqTechWords []string, questionText, faqText string) bool {
	// 定义技术领域
	domains := map[string][]string{
		"go":         {"go", "golang"},
		"python":     {"python", "py"},
		"java":       {"java"},
		"javascript": {"javascript", "js", "node", "nodejs"},
		"database":   {"mysql", "redis", "mongodb", "postgresql", "sql"},
		"ai":         {"localai", "ai", "embedding", "vector", "rag", "llm"},
		"system":     {"docker", "kubernetes", "linux", "windows", "nginx", "apache"},
	}

	// 获取问题和FAQ的技术领域
	questionDomains := sm.getTechnicalDomains(questionTechWords, questionText, domains)
	faqDomains := sm.getTechnicalDomains(faqTechWords, faqText, domains)

	// 如果都没有明确的技术领域，认为是通用问题
	if len(questionDomains) == 0 && len(faqDomains) == 0 {
		return true
	}

	// 如果一个有技术领域，另一个没有，不匹配
	if len(questionDomains) == 0 || len(faqDomains) == 0 {
		return false
	}

	// 检查是否有共同的技术领域
	for _, qDomain := range questionDomains {
		for _, fDomain := range faqDomains {
			if qDomain == fDomain {
				return true
			}
		}
	}

	return false
}

// getTechnicalDomains 获取文本中的技术领域
func (sm *SmartMatcher) getTechnicalDomains(techWords []string, text string, domains map[string][]string) []string {
	var foundDomains []string

	for domain, keywords := range domains {
		for _, keyword := range keywords {
			// 检查技术词汇中是否包含
			for _, techWord := range techWords {
				if techWord == keyword {
					foundDomains = append(foundDomains, domain)
					goto nextDomain
				}
			}
			// 检查文本中是否包含
			if strings.Contains(text, keyword) {
				foundDomains = append(foundDomains, domain)
				goto nextDomain
			}
		}
	nextDomain:
	}

	return foundDomains
}

// setMatchTypeAndExplanation 设置匹配类型和解释
func (sm *SmartMatcher) setMatchTypeAndExplanation(result *MatchResult, question string, questionType QuestionType) {
	if result.Score > 0.8 {
		result.MatchType = "精确匹配"
		result.Explanation = "找到了与您问题高度匹配的答案"
	} else if result.Score > 0.6 {
		result.MatchType = "语义匹配"
		result.Explanation = "基于语义理解找到了相关答案"
	} else if result.Score > 0.4 {
		result.MatchType = "相关匹配"
		result.Explanation = "找到了可能相关的信息"
	} else {
		result.MatchType = "模糊匹配"
		result.Explanation = "找到了一些可能有用的信息"
	}
}

// getFAQByID 根据ID获取FAQ
func (sm *SmartMatcher) getFAQByID(id int) *mysql.FAQ {
	for _, faq := range sm.faqs {
		if faq.ID == id {
			return &faq
		}
	}
	return nil
}

// hasTopicConflict 检查问题和FAQ是否存在主题冲突
func (sm *SmartMatcher) hasTopicConflict(question string, faq mysql.FAQ) bool {
	questionTopic := sm.identifyTopic(strings.ToLower(question))
	faqQuestionTopic := sm.identifyTopic(strings.ToLower(faq.Question))
	faqAnswerTopic := sm.identifyTopic(strings.ToLower(faq.Answer))

	// 使用问题和答案中更明确的主题
	faqTopic := faqQuestionTopic
	if faqAnswerTopic != "general" && faqAnswerTopic != faqQuestionTopic {
		faqTopic = faqAnswerTopic
	}

	// 检查主题冲突
	isConflicting := sm.areTopicsConflicting(questionTopic, faqTopic)

	if isConflicting {
		// 记录主题冲突日志
		log.Printf("🚫 SemanticMatcher检测到主题冲突: %s vs %s (问题: %s)",
			questionTopic, faqTopic, faq.Question)
	}

	return isConflicting
}

// identifyTopic 识别文本主题
func (sm *SmartMatcher) identifyTopic(text string) string {
	textLower := strings.ToLower(text)

	// 编程语言主题
	programmingLanguages := map[string]string{
		"go语言": "go", "golang": "go", "go编程": "go", "go开发": "go",
		"python": "python", "python编程": "python", "python开发": "python",
		"java": "java", "java编程": "java", "java开发": "java",
		"javascript": "javascript", "js": "javascript", "前端": "javascript",
		"c++": "cpp", "cpp": "cpp", "c语言": "c",
		"c#": "csharp", "csharp": "csharp", ".net": "csharp",
		"rust": "rust", "php": "php", "ruby": "ruby",
	}

	// 数据库主题
	databases := map[string]string{
		"mysql": "mysql", "postgresql": "postgresql", "postgres": "postgresql",
		"mongodb": "mongodb", "mongo": "mongodb", "redis": "redis",
		"sqlite": "sqlite", "oracle": "oracle", "数据库": "database",
	}

	// 框架和工具主题
	frameworks := map[string]string{
		"react": "react", "vue": "vue", "angular": "angular",
		"django": "django", "flask": "flask", "spring": "spring",
		"express": "express", "nodejs": "nodejs", "node": "nodejs",
		"docker": "docker", "kubernetes": "kubernetes", "k8s": "kubernetes",
	}

	// AI和机器学习主题
	aiTopics := map[string]string{
		"localai": "ai", "人工智能": "ai", "机器学习": "ai", "深度学习": "ai",
		"神经网络": "ai", "ai": "ai", "ml": "ai", "nlp": "ai",
	}

	// 系统部署主题
	deploymentTopics := map[string]string{
		"部署": "deployment", "安装": "deployment", "配置": "deployment",
		"运维": "deployment", "服务器": "deployment", "云服务": "deployment",
		"aws": "deployment", "azure": "deployment", "阿里云": "deployment",
	}

	// 按优先级检查主题
	topicMaps := []map[string]string{
		programmingLanguages, databases, frameworks, aiTopics, deploymentTopics,
	}

	for _, topicMap := range topicMaps {
		for keyword, topic := range topicMap {
			if strings.Contains(textLower, keyword) {
				return topic
			}
		}
	}

	return "general"
}

// areTopicsConflicting 检查两个主题是否冲突
func (sm *SmartMatcher) areTopicsConflicting(topic1, topic2 string) bool {
	// 定义冲突的主题组
	conflictGroups := [][]string{
		{"go", "python", "java", "javascript", "cpp", "csharp", "rust", "php", "ruby"}, // 编程语言
		{"mysql", "postgresql", "mongodb", "redis", "sqlite", "oracle"},                // 数据库
		{"react", "vue", "angular"},              // 前端框架
		{"django", "flask", "spring", "express"}, // 后端框架
	}

	for _, group := range conflictGroups {
		found1, found2 := false, false
		for _, item := range group {
			if topic1 == item {
				found1 = true
			}
			if topic2 == item {
				found2 = true
			}
		}
		// 如果两个主题在同一组但不相同，则冲突
		if found1 && found2 && topic1 != topic2 {
			return true
		}
	}

	return false
}

// 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
