package learning

import (
	"database/sql"
	"encoding/json"
	"math"
	"strings"
	"time"

	"faq-system/internal/logger"
)

// TopicRelevance 主题相关性结果
type TopicRelevance struct {
	isConflicting  bool    // 是否存在主题冲突
	queryTopic     string  // 查询主题
	knowledgeTopic string  // 知识主题
	conflictScore  float64 // 冲突分数
	relevanceScore float64 // 相关性分数
}

// SmartEntity 智能实体
type SmartEntity struct {
	Text       string            `json:"text"`
	Type       string            `json:"type"`       // 实体类型：technology, person, concept, etc.
	Category   string            `json:"category"`   // 细分类别：programming_language, framework, etc.
	Confidence float64           `json:"confidence"` // 识别置信度
	Context    string            `json:"context"`    // 上下文信息
	Attributes map[string]string `json:"attributes"` // 额外属性
}

// EntitySimilarity 实体相似度结果
type EntitySimilarity struct {
	exactMatch         bool    // 是否完全匹配
	semanticSimilarity float64 // 语义相似度
	conflictDetected   bool    // 是否检测到冲突
	matchedEntity      string  // 匹配的实体
	queryEntity        string  // 查询实体
	conflictEntity     string  // 冲突实体
}

// SearchLearnedKnowledge 搜索学习到的知识 - 使用NLP增强搜索
func (kl *KnowledgeLearner) SearchLearnedKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🔍 开始NLP增强搜索学习知识: %s", query)

	// 1. 首先尝试knowledge_vectors表的向量搜索（最精确）
	if kl.hasVectorData() {
		logger.Infof("🔍 尝试knowledge_vectors向量搜索...")
		vectorResults, err := kl.vectorSearchFromKnowledgeVectors(query, limit)
		if err != nil {
			logger.Warnf("knowledge_vectors向量搜索失败: %v", err)
		} else if len(vectorResults) > 0 {
			// 使用NLP分析提高匹配质量
			enhancedResults := kl.enhanceResultsWithNLP(query, vectorResults)
			if len(enhancedResults) > 0 {
				logger.Infof("✅ knowledge_vectors向量搜索+NLP增强找到 %d 个结果", len(enhancedResults))
				return enhancedResults, nil
			}
		} else {
			logger.Infof("knowledge_vectors向量搜索未找到结果")
		}
	} else {
		logger.Infof("knowledge_vectors表无数据，使用NLP文本搜索")
	}

	// 2. 使用NLP增强的文本搜索
	logger.Infof("🔍 尝试NLP增强文本搜索...")
	results, err := kl.nlpEnhancedTextSearch(query, limit)
	if err != nil {
		logger.Errorf("NLP增强文本搜索失败: %v", err)
		// 3. 最后回退到基础文本搜索
		logger.Infof("🔍 回退到基础文本搜索...")
		basicResults, basicErr := kl.textSearchKnowledge(query, limit)
		if basicErr != nil {
			logger.Errorf("基础文本搜索也失败: %v", basicErr)
			return nil, basicErr
		}
		logger.Infof("✅ 基础文本搜索找到 %d 个结果", len(basicResults))
		return basicResults, nil
	}
	logger.Infof("✅ NLP增强文本搜索找到 %d 个结果", len(results))
	return results, nil
}

// vectorSearchKnowledge 向量搜索学习知识
func (kl *KnowledgeLearner) vectorSearchKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	// 使用NLP处理器生成查询向量
	var queryVector []float32
	var err error

	if kl.nlpProcessor != nil {
		if processor, ok := kl.nlpProcessor.(interface {
			GenerateEmbedding(string) ([]float32, error)
		}); ok {
			queryVector, err = processor.GenerateEmbedding(query)
			if err != nil {
				logger.Warnf("NLP处理器生成查询向量失败: %v", err)
				return nil, err
			}
		}
	}

	if len(queryVector) == 0 {
		logger.Info("无法生成查询向量，跳过向量搜索")
		return []*LearnedKnowledge{}, nil
	}

	// 获取所有知识向量并计算相似度
	vectorQuery := `
		SELECT lk.id, lk.question, lk.answer, lk.source, lk.confidence, 
		       lk.category, lk.keywords, lk.context, lk.learned_from, 
		       lk.status, lk.created_at, kv.vector_data
		FROM learned_knowledge lk
		JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.status IN ('approved', 'pending')
		ORDER BY lk.confidence DESC
	`

	rows, err := kl.db.Query(vectorQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	type knowledgeWithVector struct {
		knowledge  *LearnedKnowledge
		vector     []float32
		similarity float32
	}

	var candidates []knowledgeWithVector

	for rows.Next() {
		var knowledge LearnedKnowledge
		var vectorJSON, keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt, &vectorJSON)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)

		// 解析向量
		var vector []float32
		if err := json.Unmarshal([]byte(vectorJSON), &vector); err != nil {
			continue
		}

		// 计算相似度
		similarity := cosineSimilarity(queryVector, vector)

		candidates = append(candidates, knowledgeWithVector{
			knowledge:  &knowledge,
			vector:     vector,
			similarity: similarity,
		})
	}

	// 按相似度排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].similarity < candidates[j].similarity {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	// 返回前N个结果
	if limit > len(candidates) {
		limit = len(candidates)
	}

	results := make([]*LearnedKnowledge, limit)
	for i := 0; i < limit; i++ {
		results[i] = candidates[i].knowledge
	}

	return results, nil
}

// textSearchKnowledge 文本搜索学习知识
func (kl *KnowledgeLearner) textSearchKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	// 先检查数据库中是否有学习知识
	var totalCount int
	countQuery := "SELECT COUNT(*) FROM learned_knowledge WHERE status IN ('approved', 'pending')"
	err := kl.db.QueryRow(countQuery).Scan(&totalCount)
	if err != nil {
		logger.Errorf("检查学习知识总数失败: %v", err)
	} else {
		logger.Infof("📊 数据库中共有 %d 条可用学习知识", totalCount)
	}

	// 使用应用层搜索避免数据库字符集问题
	logger.Infof("🔍 使用应用层搜索避免字符集问题")
	return kl.fallbackTextSearch(query, limit)

}

// cosineSimilarity 计算余弦相似度
func cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// ApproveKnowledge 批准学习到的知识
func (kl *KnowledgeLearner) ApproveKnowledge(knowledgeID int) error {
	query := `UPDATE learned_knowledge SET status = 'approved' WHERE id = ?`
	_, err := kl.db.Exec(query, knowledgeID)
	if err != nil {
		return err
	}

	logger.Infof("✅ 知识 %d 已批准", knowledgeID)
	return nil
}

// GetPendingKnowledge 获取待审核的知识
func (kl *KnowledgeLearner) GetPendingKnowledge(limit int) ([]*LearnedKnowledge, error) {
	query := `
		SELECT id, question, answer, source, confidence, category, 
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status = 'pending'
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		results = append(results, &knowledge)
	}

	return results, nil
}

// RecordKnowledgeUsage 记录知识使用情况
func (kl *KnowledgeLearner) RecordKnowledgeUsage(knowledgeID int, queryID int64, userID string, matchScore float32, wasHelpful *bool) error {
	query := `
		INSERT INTO knowledge_usage 
		(knowledge_id, query_id, user_id, match_score, was_helpful)
		VALUES (?, ?, ?, ?, ?)
	`

	_, err := kl.db.Exec(query, knowledgeID, queryID, userID, matchScore, wasHelpful)
	if err != nil {
		return err
	}

	// 更新知识的使用统计
	updateQuery := `
		UPDATE learned_knowledge 
		SET usage_count = usage_count + 1,
		    last_used = NOW()
		WHERE id = ?
	`
	kl.db.Exec(updateQuery, knowledgeID)

	return nil
}

// GetKnowledgeStats 获取知识统计信息
func (kl *KnowledgeLearner) GetKnowledgeStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总知识数量
	var totalCount int
	kl.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&totalCount)
	stats["total_knowledge"] = totalCount

	// 按状态分组
	statusQuery := `
		SELECT status, COUNT(*) as count 
		FROM learned_knowledge 
		GROUP BY status
	`
	rows, err := kl.db.Query(statusQuery)
	if err == nil {
		statusStats := make(map[string]int)
		for rows.Next() {
			var status string
			var count int
			rows.Scan(&status, &count)
			statusStats[status] = count
		}
		rows.Close()
		stats["by_status"] = statusStats
	}

	// 按分类分组
	categoryQuery := `
		SELECT category, COUNT(*) as count 
		FROM learned_knowledge 
		GROUP BY category
	`
	rows, err = kl.db.Query(categoryQuery)
	if err == nil {
		categoryStats := make(map[string]int)
		for rows.Next() {
			var category string
			var count int
			rows.Scan(&category, &count)
			categoryStats[category] = count
		}
		rows.Close()
		stats["by_category"] = categoryStats
	}

	// 最近学习的知识
	var recentCount int
	kl.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)").Scan(&recentCount)
	stats["recent_learned"] = recentCount

	return stats, nil
}

// fallbackTextSearch 备用文本搜索方法，避免字符集问题
func (kl *KnowledgeLearner) fallbackTextSearch(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🔄 使用备用搜索方案")

	// 最简单的查询，不使用任何字符串匹配函数
	searchQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(searchQuery, limit*3) // 获取更多结果用于过滤
	if err != nil {
		logger.Errorf("备用搜索也失败: %v", err)
		return nil, err
	}
	defer rows.Close()

	var allResults []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time
		var learnedFrom sql.NullString // 使用sql.NullString处理NULL值

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &learnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			logger.Errorf("扫描行失败: %v", err)
			continue
		}

		// 处理NULL值
		if learnedFrom.Valid {
			knowledge.LearnedFrom = learnedFrom.String
		} else {
			knowledge.LearnedFrom = ""
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		allResults = append(allResults, &knowledge)
	}

	// 在应用层进行文本匹配过滤（包含主题检查）
	var results []*LearnedKnowledge
	queryLower := strings.ToLower(query)

	for _, knowledge := range allResults {
		questionLower := strings.ToLower(knowledge.Question)
		answerLower := strings.ToLower(knowledge.Answer)

		// 先进行主题检查
		queryTopic := kl.identifyTopic(queryLower)
		questionTopic := kl.identifyTopic(questionLower)

		if kl.areTopicsConflicting(queryTopic, questionTopic) {
			logger.Infof("🚫 备用搜索结果被主题检查拒绝: %s (主题冲突: %s vs %s)", knowledge.Question, queryTopic, questionTopic)
			continue
		}

		if strings.Contains(questionLower, queryLower) || strings.Contains(answerLower, queryLower) {
			results = append(results, knowledge)
			if len(results) >= limit {
				break
			}
		}
	}

	logger.Infof("✅ 备用搜索找到 %d 个匹配结果", len(results))
	return results, nil
}

// enhanceResultsWithNLP 使用NLP分析增强搜索结果
func (kl *KnowledgeLearner) enhanceResultsWithNLP(query string, results []*LearnedKnowledge) []*LearnedKnowledge {
	if len(results) == 0 {
		return results
	}

	logger.Infof("🧠 使用NLP分析增强 %d 个搜索结果（包含主题检查）", len(results))

	// 对每个结果进行NLP分析评分
	type scoredResult struct {
		knowledge *LearnedKnowledge
		nlpScore  float64
	}

	var scoredResults []scoredResult
	queryLower := strings.ToLower(query)

	for _, knowledge := range results {
		score := kl.calculateNLPScore(queryLower, knowledge)
		if score > 0.1 { // 只保留有一定相关性的结果
			scoredResults = append(scoredResults, scoredResult{
				knowledge: knowledge,
				nlpScore:  score,
			})
		} else if score == 0.0 {
			// 如果分数为0，说明被主题检查拒绝了
			logger.Infof("🚫 向量搜索结果被主题检查拒绝: %s", knowledge.Question)
		}
	}

	// 按NLP评分排序
	for i := 0; i < len(scoredResults)-1; i++ {
		for j := i + 1; j < len(scoredResults); j++ {
			if scoredResults[i].nlpScore < scoredResults[j].nlpScore {
				scoredResults[i], scoredResults[j] = scoredResults[j], scoredResults[i]
			}
		}
	}

	// 返回增强后的结果
	var enhancedResults []*LearnedKnowledge
	for _, scored := range scoredResults {
		// 更新置信度为NLP评分（修复类型转换）
		scored.knowledge.Confidence = float32(scored.nlpScore)
		enhancedResults = append(enhancedResults, scored.knowledge)
	}

	logger.Infof("✅ NLP增强完成，保留 %d 个高质量结果", len(enhancedResults))
	return enhancedResults
}

// calculateNLPScore 计算NLP相关性评分 - 严格的主题相关性检查
func (kl *KnowledgeLearner) calculateNLPScore(query string, knowledge *LearnedKnowledge) float64 {
	score := 0.0
	queryLower := strings.ToLower(query)
	questionLower := strings.ToLower(knowledge.Question)
	answerLower := strings.ToLower(knowledge.Answer)

	// 0. 简化的主题相关性检查
	queryTopic := kl.identifyTopic(queryLower)
	questionTopic := kl.identifyTopic(questionLower)

	// 检查是否为明显的跨主题冲突
	if kl.areTopicsConflicting(queryTopic, questionTopic) {
		logger.Infof("❌ 主题冲突: %s vs %s - 拒绝匹配 (问题: %s)", queryTopic, questionTopic, knowledge.Question)
		return 0.0 // 直接返回0分，拒绝跨主题匹配
	}

	// 🔍 调试日志：显示主题识别结果（仅对可能有问题的匹配）
	if queryTopic != questionTopic && queryTopic != "general" && questionTopic != "general" {
		logger.Infof("⚠️ 不同主题但未冲突 - 查询: '%s' → %s, 知识: '%s' → %s",
			queryLower, queryTopic, knowledge.Question, questionTopic)
	}

	// 1. 使用NLP处理器进行语义向量分析
	if kl.nlpProcessor != nil {
		if processor, ok := kl.nlpProcessor.(interface{ ProcessText(string) interface{} }); ok {
			// 分析查询和知识的语义向量
			queryResult := processor.ProcessText(queryLower)
			questionResult := processor.ProcessText(questionLower)

			// 计算语义相似度
			semanticScore := kl.calculateSemanticSimilarityFromResults(queryResult, questionResult)
			if semanticScore > 0.9 {
				score += 1.0 // 高语义相似度
				logger.Infof("🎯 高语义相似度匹配: %.3f", semanticScore)
				return score
			} else if semanticScore > 0.7 {
				score += semanticScore
				logger.Infof("🎯 中等语义相似度匹配: %.3f", semanticScore)
				return score
			}

			// 检测语义冲突
			conflictScore := kl.detectSemanticConflict(queryResult, questionResult)
			if conflictScore > 0.8 {
				score -= conflictScore
				logger.Infof("⚠️ 语义冲突检测: %.3f", conflictScore)
			}
		}
	}

	// 2. 传统精确匹配（降低权重，避免"什么是"等通用词干扰）
	if strings.Contains(questionLower, queryLower) {
		score += 0.6 // 降低通用匹配权重
		questionPreview := questionLower
		if len(questionPreview) > 50 {
			questionPreview = questionPreview[:50] + "..."
		}
		logger.Infof("🎯 传统匹配-问题包含查询: %s 在 %s", queryLower, questionPreview)
	}
	if strings.Contains(answerLower, queryLower) {
		score += 0.5 // 降低答案匹配权重
		logger.Infof("🎯 传统匹配-答案包含查询: %s 在答案中", queryLower)
	}
	if strings.Contains(queryLower, questionLower) {
		score += 0.7 // 查询词包含问题，也很相关
		questionPreview2 := questionLower
		if len(questionPreview2) > 50 {
			questionPreview2 = questionPreview2[:50] + "..."
		}
		logger.Infof("🎯 精确匹配-查询包含问题: %s 包含 %s", queryLower, questionPreview2)
	}

	// 2. 中文字符匹配（对于"蔡依林"这样的名字特别重要）
	queryRunes := []rune(queryLower)
	questionRunes := []rune(questionLower)
	answerRunes := []rune(answerLower)

	// 计算中文字符重叠度
	runeOverlapQuestion := kl.calculateRuneOverlap(queryRunes, questionRunes)
	runeOverlapAnswer := kl.calculateRuneOverlap(queryRunes, answerRunes)

	score += runeOverlapQuestion * 0.6
	score += runeOverlapAnswer * 0.5

	// 3. 词汇匹配（分词后的匹配）
	queryWords := strings.Fields(queryLower)
	questionWords := strings.Fields(questionLower)
	answerWords := strings.Fields(answerLower)

	// 计算词汇重叠度
	questionOverlap := kl.calculateWordOverlap(queryWords, questionWords)
	answerOverlap := kl.calculateWordOverlap(queryWords, answerWords)

	score += questionOverlap * 0.4
	score += answerOverlap * 0.3

	// 4. 关键词标签匹配
	if len(knowledge.Keywords) > 0 {
		for _, keyword := range knowledge.Keywords {
			keywordLower := strings.ToLower(keyword)
			if strings.Contains(queryLower, keywordLower) || strings.Contains(keywordLower, queryLower) {
				score += 0.3 // 提高关键词匹配权重
			}
		}
	}

	// 5. 语义相似度（简单实现）
	semanticScore := kl.calculateSemanticSimilarity(queryLower, questionLower)
	score += semanticScore * 0.2

	// 6. 特殊加分：如果是人名查询，给予额外加分
	if kl.isPersonName(queryLower) {
		if strings.Contains(questionLower, queryLower) || strings.Contains(answerLower, queryLower) {
			score += 0.2 // 人名匹配额外加分
		}
	}

	// 限制评分范围
	if score > 1.0 {
		score = 1.0
	}

	return score
}

// calculateWordOverlap 计算词汇重叠度
func (kl *KnowledgeLearner) calculateWordOverlap(words1, words2 []string) float64 {
	if len(words1) == 0 || len(words2) == 0 {
		return 0.0
	}

	// 创建词汇集合
	wordSet1 := make(map[string]bool)
	for _, word := range words1 {
		if len(word) > 1 { // 忽略单字符词
			wordSet1[word] = true
		}
	}

	// 计算重叠词汇数量
	overlap := 0
	for _, word := range words2 {
		if len(word) > 1 && wordSet1[word] {
			overlap++
		}
	}

	// 返回重叠比例
	maxLen := len(words1)
	if len(words2) > maxLen {
		maxLen = len(words2)
	}

	return float64(overlap) / float64(maxLen)
}

// calculateSemanticSimilarity 计算语义相似度（简单实现）
func (kl *KnowledgeLearner) calculateSemanticSimilarity(text1, text2 string) float64 {
	// 简单的语义相似度计算
	// 基于共同子串和编辑距离

	// 1. 最长公共子序列
	lcs := kl.longestCommonSubsequence(text1, text2)
	lcsScore := float64(lcs) / float64(max(len(text1), len(text2)))

	// 2. 编辑距离相似度
	editDist := kl.editDistance(text1, text2)
	maxLen := max(len(text1), len(text2))
	editScore := 1.0 - float64(editDist)/float64(maxLen)

	// 综合评分
	return (lcsScore*0.6 + editScore*0.4)
}

// longestCommonSubsequence 计算最长公共子序列长度
func (kl *KnowledgeLearner) longestCommonSubsequence(text1, text2 string) int {
	m, n := len(text1), len(text2)
	if m == 0 || n == 0 {
		return 0
	}

	// 动态规划表
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 填充DP表
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if text1[i-1] == text2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = max(dp[i-1][j], dp[i][j-1])
			}
		}
	}

	return dp[m][n]
}

// editDistance 计算编辑距离
func (kl *KnowledgeLearner) editDistance(text1, text2 string) int {
	m, n := len(text1), len(text2)

	// 创建DP表
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 初始化边界条件
	for i := 0; i <= m; i++ {
		dp[i][0] = i
	}
	for j := 0; j <= n; j++ {
		dp[0][j] = j
	}

	// 填充DP表
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if text1[i-1] == text2[j-1] {
				dp[i][j] = dp[i-1][j-1]
			} else {
				dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1
			}
		}
	}

	return dp[m][n]
}

// max 返回两个整数的最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// min 返回三个整数的最小值
func min(a, b, c int) int {
	if a < b && a < c {
		return a
	}
	if b < c {
		return b
	}
	return c
}

// nlpEnhancedTextSearch NLP增强的文本搜索
func (kl *KnowledgeLearner) nlpEnhancedTextSearch(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🧠 开始NLP增强文本搜索: %s", query)

	// 获取所有可用的学习知识 - 优先查询包含关键词的数据
	queryPattern := "%" + query + "%"
	searchQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, knowledge_topic, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY
			CASE
				WHEN question LIKE ? OR answer LIKE ? OR keywords LIKE ? THEN 0
				ELSE 1
			END,
			confidence DESC,
			created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(searchQuery, queryPattern, queryPattern, queryPattern, limit*50) // 大幅增加候选结果数量
	if err != nil {
		logger.Errorf("查询学习知识失败: %v", err)
		return nil, err
	}
	defer rows.Close()

	var candidates []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time
		var learnedFrom sql.NullString
		var knowledgeTopic sql.NullString

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &learnedFrom,
			&knowledge.Status, &knowledgeTopic, &createdAt)
		if err != nil {
			logger.Errorf("扫描行失败: %v", err)
			continue
		}

		// 处理NULL值
		if learnedFrom.Valid {
			knowledge.LearnedFrom = learnedFrom.String
		} else {
			knowledge.LearnedFrom = ""
		}

		if knowledgeTopic.Valid {
			knowledge.KnowledgeTopic = knowledgeTopic.String
		} else {
			knowledge.KnowledgeTopic = ""
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		candidates = append(candidates, &knowledge)
	}

	logger.Infof("📊 获取到 %d 个候选知识进行NLP分析", len(candidates))

	// 使用NLP分析对所有候选结果进行评分
	type scoredKnowledge struct {
		knowledge *LearnedKnowledge
		nlpScore  float64
	}

	var scoredResults []scoredKnowledge
	queryLower := strings.ToLower(query)

	for _, knowledge := range candidates {
		score := kl.calculateNLPScore(queryLower, knowledge)
		questionPreview := knowledge.Question
		if len(questionPreview) > 50 {
			questionPreview = questionPreview[:50] + "..."
		}
		logger.Infof("🔍 候选知识评分: %s -> %.3f", questionPreview, score)

		// 如果包含查询关键词，额外记录详细信息
		if strings.Contains(strings.ToLower(knowledge.Question), queryLower) ||
			strings.Contains(strings.ToLower(knowledge.Answer), queryLower) {
			logger.Infof("🎯 发现关键词匹配: %s", knowledge.Question)
		}

		if score > 0.05 { // 大幅降低阈值，包含更多可能相关的结果
			scoredResults = append(scoredResults, scoredKnowledge{
				knowledge: knowledge,
				nlpScore:  score,
			})
		}
	}

	// 按NLP评分排序
	for i := 0; i < len(scoredResults)-1; i++ {
		for j := i + 1; j < len(scoredResults); j++ {
			if scoredResults[i].nlpScore < scoredResults[j].nlpScore {
				scoredResults[i], scoredResults[j] = scoredResults[j], scoredResults[i]
			}
		}
	}

	// 🛡️ 质量检查：如果最高分数太低，说明没有真正相关的内容
	if len(scoredResults) == 0 || scoredResults[0].nlpScore < 0.3 {
		logger.Infof("🚫 NLP增强搜索：最高评分 %.3f 低于质量阈值 0.3，返回空结果",
			func() float64 {
				if len(scoredResults) > 0 {
					return scoredResults[0].nlpScore
				}
				return 0.0
			}())
		return []*LearnedKnowledge{}, nil
	}

	// 🛡️ 问答格式检查：确保答案不是问题
	for i := 0; i < len(scoredResults); i++ {
		answer := scoredResults[i].knowledge.Answer
		question := scoredResults[i].knowledge.Question

		if kl.isAnswerActuallyQuestion(answer) {
			logger.Infof("🚫 检测到答案是问题格式，跳过: %s (问题: %s)", answer, question)

			// 🔧 尝试修复数据：如果答案是问题，可能数据有错误
			kl.reportDataIssue(scoredResults[i].knowledge.ID, question, answer)

			// 移除这个结果
			scoredResults = append(scoredResults[:i], scoredResults[i+1:]...)
			i-- // 调整索引
		}
	}

	// 再次检查是否还有有效结果
	if len(scoredResults) == 0 {
		logger.Infof("🚫 所有结果都被问答格式检查过滤，返回空结果")
		return []*LearnedKnowledge{}, nil
	}

	// 返回前N个最佳结果
	resultLimit := limit
	if len(scoredResults) < resultLimit {
		resultLimit = len(scoredResults)
	}

	var results []*LearnedKnowledge
	for i := 0; i < resultLimit; i++ {
		// 🛡️ 只返回评分超过阈值的结果
		if scoredResults[i].nlpScore < 0.3 {
			logger.Infof("🚫 跳过低质量结果: %s (评分: %.3f)",
				scoredResults[i].knowledge.Question, scoredResults[i].nlpScore)
			break
		}

		// 更新置信度为NLP评分
		scoredResults[i].knowledge.Confidence = float32(scoredResults[i].nlpScore)
		results = append(results, scoredResults[i].knowledge)

		questionPreview := scoredResults[i].knowledge.Question
		if len(questionPreview) > 50 {
			questionPreview = questionPreview[:50] + "..."
		}
		logger.Infof("🎯 NLP匹配结果 %d: %s (评分: %.3f)",
			i+1, questionPreview, scoredResults[i].nlpScore)
	}

	if len(results) == 0 {
		logger.Infof("🚫 NLP增强搜索：所有结果都低于质量阈值，返回空结果")
		return []*LearnedKnowledge{}, nil
	}

	logger.Infof("✅ NLP增强文本搜索完成，返回 %d 个高质量结果", len(results))
	return results, nil
}

// calculateRuneOverlap 计算中文字符重叠度
func (kl *KnowledgeLearner) calculateRuneOverlap(runes1, runes2 []rune) float64 {
	if len(runes1) == 0 || len(runes2) == 0 {
		return 0.0
	}

	// 创建字符集合
	runeSet1 := make(map[rune]bool)
	for _, r := range runes1 {
		runeSet1[r] = true
	}

	// 计算重叠字符数量
	overlap := 0
	for _, r := range runes2 {
		if runeSet1[r] {
			overlap++
		}
	}

	// 返回重叠比例
	maxLen := len(runes1)
	if len(runes2) > maxLen {
		maxLen = len(runes2)
	}

	return float64(overlap) / float64(maxLen)
}

// isPersonName 判断是否是人名（简单实现）
func (kl *KnowledgeLearner) isPersonName(query string) bool {
	// 简单的人名判断规则
	runes := []rune(query)

	// 长度在2-4个字符之间，通常是中文人名
	if len(runes) >= 2 && len(runes) <= 4 {
		// 检查是否全部是中文字符
		for _, r := range runes {
			if r < 0x4e00 || r > 0x9fff {
				return false // 不是中文字符
			}
		}
		return true
	}

	return false
}

// hasVectorData 检查是否有向量数据
func (kl *KnowledgeLearner) hasVectorData() bool {
	var count int
	err := kl.db.QueryRow("SELECT COUNT(*) FROM knowledge_vectors").Scan(&count)
	if err != nil {
		logger.Warnf("检查向量数据失败: %v", err)
		return false
	}
	return count > 0
}

// vectorSearchFromKnowledgeVectors 从knowledge_vectors表进行向量搜索
func (kl *KnowledgeLearner) vectorSearchFromKnowledgeVectors(query string, limit int) ([]*LearnedKnowledge, error) {
	// 首先尝试使用NLP处理器生成查询向量
	var queryVector []float32
	var err error

	// 检查是否有NLP处理器
	if kl.nlpProcessor != nil {
		// 使用类型断言检查是否有ProcessText方法
		if processor, ok := kl.nlpProcessor.(interface{ ProcessText(string) interface{} }); ok {
			result := processor.ProcessText(query)
			if result != nil {
				// 从NLP结果生成向量（简化实现）
				queryVector = kl.generateVectorFromNLPResult(result, query)
			}
		}
	}

	// embedClient已移除，只使用NLP处理器生成向量
	// 如果上面的NLP处理器没有生成向量，则跳过向量搜索

	// 如果都不可用，返回空结果
	if len(queryVector) == 0 {
		logger.Infof("无法生成查询向量，跳过向量搜索")
		return []*LearnedKnowledge{}, nil
	}

	// 从knowledge_vectors表查询所有向量数据
	vectorQuery := `
		SELECT lk.id, lk.question, lk.answer, lk.source, lk.confidence,
		       lk.category, lk.keywords, lk.context, lk.learned_from,
		       lk.status, lk.knowledge_topic, lk.created_at, kv.vector_data
		FROM learned_knowledge lk
		JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.status IN ('approved', 'pending')
		ORDER BY lk.confidence DESC
	`

	rows, err := kl.db.Query(vectorQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	type knowledgeWithVector struct {
		knowledge  *LearnedKnowledge
		vector     []float32
		similarity float32
	}

	var candidates []knowledgeWithVector

	for rows.Next() {
		var knowledge LearnedKnowledge
		var vectorJSON, keywordsJSON string
		var createdAt time.Time
		var knowledgeTopic sql.NullString

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &knowledgeTopic, &createdAt, &vectorJSON)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		if knowledgeTopic.Valid {
			knowledge.KnowledgeTopic = knowledgeTopic.String
		} else {
			knowledge.KnowledgeTopic = ""
		}
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)

		// 解析向量
		var vector []float32
		if err := json.Unmarshal([]byte(vectorJSON), &vector); err != nil {
			continue
		}

		// 计算相似度
		similarity := kl.cosineSimilarity(queryVector, vector)

		candidates = append(candidates, knowledgeWithVector{
			knowledge:  &knowledge,
			vector:     vector,
			similarity: similarity,
		})
	}

	// 按相似度排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].similarity < candidates[j].similarity {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	// 返回前N个结果
	var results []*LearnedKnowledge
	maxResults := limit
	if maxResults > len(candidates) {
		maxResults = len(candidates)
	}

	for i := 0; i < maxResults; i++ {
		if candidates[i].similarity > 0.3 { // 相似度阈值
			results = append(results, candidates[i].knowledge)
			questionPreview := candidates[i].knowledge.Question
			if len(questionPreview) > 50 {
				questionPreview = questionPreview[:50] + "..."
			}
			logger.Infof("🎯 向量匹配: %s (相似度: %.3f)", questionPreview, candidates[i].similarity)
		}
	}

	return results, nil
}

// generateVectorFromNLPResult 从NLP结果生成向量
func (kl *KnowledgeLearner) generateVectorFromNLPResult(nlpResult interface{}, text string) []float32 {
	// 简化实现：基于文本内容生成模拟向量
	hash := 0
	for _, char := range text {
		hash = hash*31 + int(char)
	}

	// 生成384维向量
	vector := make([]float32, 384)
	for i := range vector {
		hash = (hash*1103515245 + 12345) & 0x7fffffff
		vector[i] = float32(hash%1000-500) / 1000.0
	}

	// 归一化向量
	var norm float32
	for _, val := range vector {
		norm += val * val
	}
	if norm > 0 {
		norm = 1.0 / float32(math.Sqrt(float64(norm)))
		for i := range vector {
			vector[i] *= norm
		}
	}

	return vector
}

// cosineSimilarity 计算余弦相似度
func (kl *KnowledgeLearner) cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// extractCoreTechnicalTerms 提取核心技术词汇
func (kl *KnowledgeLearner) extractCoreTechnicalTerms(text string) []string {
	// 预定义的技术词汇列表（更精确的匹配）
	technicalTerms := []string{
		"localai", "javascript", "python", "java", "golang", "go语言", "rust", "c++", "c#",
		"react", "vue", "angular", "nodejs", "express", "django", "flask",
		"mysql", "postgresql", "mongodb", "redis", "elasticsearch",
		"docker", "kubernetes", "aws", "azure", "gcp",
		"ai", "ml", "nlp", "深度学习", "机器学习", "人工智能",
		"区块链", "比特币", "以太坊", "智能合约",
		"微服务", "分布式", "云计算", "大数据",
		"算法", "数据结构", "设计模式",
	}

	var foundTerms []string
	textLower := strings.ToLower(text)

	// 1. 精确匹配预定义技术词汇
	for _, term := range technicalTerms {
		if strings.Contains(textLower, term) {
			foundTerms = append(foundTerms, term)
		}
	}

	// 2. 特殊处理：Go语言的多种表达方式
	goTerms := []string{"go语言", "golang", "go编程", "go开发"}
	for _, goTerm := range goTerms {
		if strings.Contains(textLower, goTerm) {
			foundTerms = append(foundTerms, "go语言") // 统一为"go语言"
			break
		}
	}

	// 3. 使用gojieba分词，提取可能的技术词汇
	if kl.nlpProcessor != nil {
		if processor, ok := kl.nlpProcessor.(interface{ SegmentText(string) []string }); ok {
			words := processor.SegmentText(text)
			for _, word := range words {
				wordLower := strings.ToLower(word)
				// 检查是否为技术相关词汇
				if kl.isTechnicalTerm(wordLower) {
					foundTerms = append(foundTerms, wordLower)
				}
			}
		}
	}

	// 去重
	seen := make(map[string]bool)
	var uniqueTerms []string
	for _, term := range foundTerms {
		if !seen[term] {
			seen[term] = true
			uniqueTerms = append(uniqueTerms, term)
		}
	}

	return uniqueTerms
}

// isTechnicalTerm 判断是否为技术术语
func (kl *KnowledgeLearner) isTechnicalTerm(word string) bool {
	// 长度检查
	if len(word) < 2 {
		return false
	}

	// 包含英文字母的词汇
	if kl.containsEnglish(word) && len(word) > 2 {
		return true
	}

	// 中文技术词汇模式
	techPatterns := []string{
		"语言", "框架", "数据库", "算法", "系统", "平台", "引擎", "工具",
		"技术", "开发", "编程", "计算", "网络", "服务", "应用", "软件",
	}

	for _, pattern := range techPatterns {
		if strings.Contains(word, pattern) {
			return true
		}
	}

	return false
}

// containsEnglish 检查字符串是否包含英文字母
func (kl *KnowledgeLearner) containsEnglish(text string) bool {
	for _, r := range text {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			return true
		}
	}
	return false
}

// calculateSemanticSimilarityFromResults 计算语义相似度（基于Spago结果）
func (kl *KnowledgeLearner) calculateSemanticSimilarityFromResults(queryResult, questionResult interface{}) float64 {
	// 尝试从结果中提取语义实体和概念向量
	queryEntities := kl.extractEntitiesFromResult(queryResult)
	questionEntities := kl.extractEntitiesFromResult(questionResult)

	// 计算实体匹配度
	entitySimilarity := kl.calculateEntityMatchScore(queryEntities, questionEntities)

	// 计算概念向量相似度
	conceptSimilarity := kl.calculateConceptSimilarity(queryResult, questionResult)

	// 综合相似度
	return (entitySimilarity*0.6 + conceptSimilarity*0.4)
}

// detectSemanticConflict 检测语义冲突
func (kl *KnowledgeLearner) detectSemanticConflict(queryResult, questionResult interface{}) float64 {
	queryEntities := kl.extractEntitiesFromResult(queryResult)
	questionEntities := kl.extractEntitiesFromResult(questionResult)

	// 检测不同类别的技术实体冲突
	conflictScore := 0.0

	for _, queryEntity := range queryEntities {
		for _, questionEntity := range questionEntities {
			if kl.areConflictingEntities(queryEntity, questionEntity) {
				conflictScore += 0.8
			}
		}
	}

	return math.Min(conflictScore, 1.0)
}

// extractEntitiesFromResult 从NLP结果中提取实体
func (kl *KnowledgeLearner) extractEntitiesFromResult(result interface{}) []string {
	var entities []string

	// 尝试类型断言获取语义实体
	if spagoResult, ok := result.(map[string]interface{}); ok {
		if semanticEntities, exists := spagoResult["semantic_entities"]; exists {
			if entitiesSlice, ok := semanticEntities.([]interface{}); ok {
				for _, entity := range entitiesSlice {
					if entityMap, ok := entity.(map[string]interface{}); ok {
						if text, ok := entityMap["text"].(string); ok {
							entities = append(entities, text)
						}
					}
				}
			}
		}
	}

	return entities
}

// calculateEntityMatchScore 计算实体匹配分数
func (kl *KnowledgeLearner) calculateEntityMatchScore(queryEntities, questionEntities []string) float64 {
	if len(queryEntities) == 0 || len(questionEntities) == 0 {
		return 0.0
	}

	matches := 0
	for _, queryEntity := range queryEntities {
		for _, questionEntity := range questionEntities {
			if strings.ToLower(queryEntity) == strings.ToLower(questionEntity) {
				matches++
				break
			}
		}
	}

	return float64(matches) / float64(len(queryEntities))
}

// calculateConceptSimilarity 计算概念相似度
func (kl *KnowledgeLearner) calculateConceptSimilarity(queryResult, questionResult interface{}) float64 {
	// 简化实现：基于概念向量的余弦相似度
	queryVectors := kl.extractConceptVectors(queryResult)
	questionVectors := kl.extractConceptVectors(questionResult)

	if len(queryVectors) == 0 || len(questionVectors) == 0 {
		return 0.0
	}

	maxSimilarity := 0.0
	for _, qv := range queryVectors {
		for _, qev := range questionVectors {
			similarity := kl.cosineSimilarityFloat64(qv, qev)
			if similarity > maxSimilarity {
				maxSimilarity = similarity
			}
		}
	}

	return maxSimilarity
}

// extractConceptVectors 从结果中提取概念向量
func (kl *KnowledgeLearner) extractConceptVectors(result interface{}) [][]float64 {
	var vectors [][]float64

	// 尝试从结果中提取向量数据
	if spagoResult, ok := result.(map[string]interface{}); ok {
		if conceptVectors, exists := spagoResult["concept_vectors"]; exists {
			if vectorsSlice, ok := conceptVectors.([]interface{}); ok {
				for _, vector := range vectorsSlice {
					if vectorMap, ok := vector.(map[string]interface{}); ok {
						if vectorData, ok := vectorMap["vector"].([]interface{}); ok {
							var floatVector []float64
							for _, v := range vectorData {
								if f, ok := v.(float64); ok {
									floatVector = append(floatVector, f)
								}
							}
							if len(floatVector) > 0 {
								vectors = append(vectors, floatVector)
							}
						}
					}
				}
			}
		}
	}

	return vectors
}

// areConflictingEntities 判断两个实体是否冲突
func (kl *KnowledgeLearner) areConflictingEntities(entity1, entity2 string) bool {
	// 定义冲突的实体组
	conflictGroups := [][]string{
		{"go语言", "golang", "python", "java", "javascript", "c++", "c#", "rust"},
		{"mysql", "postgresql", "mongodb", "redis"},
		{"react", "vue", "angular"},
		{"docker", "kubernetes", "podman"},
	}

	entity1Lower := strings.ToLower(entity1)
	entity2Lower := strings.ToLower(entity2)

	for _, group := range conflictGroups {
		found1, found2 := false, false
		for _, item := range group {
			if strings.Contains(entity1Lower, item) {
				found1 = true
			}
			if strings.Contains(entity2Lower, item) {
				found2 = true
			}
		}
		// 如果两个实体在同一组但不相同，则冲突
		if found1 && found2 && entity1Lower != entity2Lower {
			return true
		}
	}

	return false
}

// cosineSimilarityFloat64 计算余弦相似度（float64版本）
func (kl *KnowledgeLearner) cosineSimilarityFloat64(a, b []float64) float64 {
	if len(a) != len(b) || len(a) == 0 {
		return 0.0
	}

	var dotProduct, normA, normB float64
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(normA) * math.Sqrt(normB))
}

// checkTopicRelevance 检查主题相关性
func (kl *KnowledgeLearner) checkTopicRelevance(query, question, answer string) TopicRelevance {
	queryTopic := kl.identifyTopic(query)
	questionTopic := kl.identifyTopic(question)
	answerTopic := kl.identifyTopic(answer)

	// 使用问题和答案中更明确的主题
	knowledgeTopic := questionTopic
	if answerTopic != "general" && answerTopic != questionTopic {
		knowledgeTopic = answerTopic
	}

	// 检查主题冲突
	isConflicting := kl.areTopicsConflicting(queryTopic, knowledgeTopic)
	conflictScore := 0.0
	relevanceScore := 0.0

	if isConflicting {
		conflictScore = 1.0
	} else if queryTopic == knowledgeTopic {
		relevanceScore = 1.0
	} else if kl.areTopicsRelated(queryTopic, knowledgeTopic) {
		relevanceScore = 0.7
	} else {
		relevanceScore = 0.3
	}

	return TopicRelevance{
		isConflicting:  isConflicting,
		queryTopic:     queryTopic,
		knowledgeTopic: knowledgeTopic,
		conflictScore:  conflictScore,
		relevanceScore: relevanceScore,
	}
}

// identifyTopic 识别文本主题
func (kl *KnowledgeLearner) identifyTopic(text string) string {
	textLower := strings.ToLower(text)

	// 编程语言主题
	programmingLanguages := map[string]string{
		"go语言": "go", "golang": "go", "go编程": "go", "go开发": "go",
		"python": "python", "python编程": "python", "python开发": "python",
		"java": "java", "java编程": "java", "java开发": "java",
		"javascript": "javascript", "js": "javascript", "前端": "javascript",
		"c++": "cpp", "cpp": "cpp", "c语言": "c",
		"c#": "csharp", "csharp": "csharp", ".net": "csharp",
		"rust": "rust", "php": "php", "ruby": "ruby",
	}

	// 数据库主题
	databases := map[string]string{
		"mysql": "mysql", "postgresql": "postgresql", "postgres": "postgresql",
		"mongodb": "mongodb", "mongo": "mongodb", "redis": "redis",
		"sqlite": "sqlite", "oracle": "oracle", "数据库": "database",
	}

	// 框架和工具主题
	frameworks := map[string]string{
		"react": "react", "vue": "vue", "angular": "angular",
		"django": "django", "flask": "flask", "spring": "spring",
		"express": "express", "nodejs": "nodejs", "node": "nodejs",
		"docker": "docker", "kubernetes": "kubernetes", "k8s": "kubernetes",
	}

	// AI和机器学习主题
	aiTopics := map[string]string{
		"localai": "ai", "local ai": "ai", "人工智能": "ai", "机器学习": "ai", "深度学习": "ai",
		"神经网络": "ai", "ai": "ai", "ml": "ai", "nlp": "ai", "大模型": "ai", "llm": "ai",
		"chatgpt": "ai", "gpt": "ai", "向量": "ai", "embedding": "ai", "嵌入": "ai",
	}

	// 系统部署主题
	deploymentTopics := map[string]string{
		"部署": "deployment", "安装": "deployment", "配置": "deployment",
		"运维": "deployment", "服务器": "deployment", "云服务": "deployment",
		"aws": "deployment", "azure": "deployment", "阿里云": "deployment",
	}

	// 按优先级检查主题
	topicMaps := []map[string]string{
		programmingLanguages, databases, frameworks, aiTopics, deploymentTopics,
	}

	for _, topicMap := range topicMaps {
		for keyword, topic := range topicMap {
			if strings.Contains(textLower, keyword) {
				return topic
			}
		}
	}

	return "general"
}

// isAnswerActuallyQuestion 检测答案是否实际上是一个问题
func (kl *KnowledgeLearner) isAnswerActuallyQuestion(answer string) bool {
	answer = strings.TrimSpace(answer)
	answerLower := strings.ToLower(answer)

	// 🔍 调试日志
	logger.Infof("🔍 检测问题格式: %s", answer)

	// 检查是否以问号结尾
	if strings.HasSuffix(answer, "？") || strings.HasSuffix(answer, "?") {
		logger.Infof("🔍 检测到问号结尾")
		// 进一步检查是否包含问题关键词
		questionKeywords := []string{
			"什么是", "如何", "怎么", "为什么", "哪个", "哪些", "谁", "何时", "何地",
			"什么", "怎样", "多少", "几个", "是否", "能否", "可以", "应该",
			"what", "how", "why", "when", "where", "who", "which", "can", "should",
		}

		for _, keyword := range questionKeywords {
			if strings.Contains(answerLower, keyword) {
				logger.Infof("🚫 检测到问题关键词: %s", keyword)
				return true
			}
		}
	}

	// 检查是否是纯问题格式（即使没有问号）
	questionPatterns := []string{
		"什么是", "如何", "怎么", "为什么", "哪个", "哪些",
		"什么", "怎样", "多少", "几个",
	}

	for _, pattern := range questionPatterns {
		if strings.HasPrefix(answerLower, pattern) {
			logger.Infof("🔍 检测到问题开头: %s", pattern)
			// 如果以问题词开头，且长度较短（可能是问题），则认为是问题
			if len(answer) < 100 { // 答案通常比问题长
				logger.Infof("🚫 检测到短问题格式")
				return true
			}
		}
	}

	logger.Infof("✅ 答案格式正常")
	return false
}

// reportDataIssue 报告数据问题
func (kl *KnowledgeLearner) reportDataIssue(knowledgeID int, question, answer string) {
	logger.Errorf("🚨 数据问题报告 - ID: %d", knowledgeID)
	logger.Errorf("   问题: %s", question)
	logger.Errorf("   答案: %s", answer)
	logger.Errorf("   问题: 答案字段包含问题格式的内容")

	// 可以在这里添加自动修复逻辑
	// 例如：标记为需要人工审核，或者尝试自动修复
}

// areTopicsConflicting 检查两个主题是否冲突
func (kl *KnowledgeLearner) areTopicsConflicting(topic1, topic2 string) bool {
	// 定义冲突的主题组
	conflictGroups := [][]string{
		{"go", "python", "java", "javascript", "cpp", "csharp", "rust", "php", "ruby"}, // 编程语言
		{"mysql", "postgresql", "mongodb", "redis", "sqlite", "oracle"},                // 数据库
		{"react", "vue", "angular"},              // 前端框架
		{"django", "flask", "spring", "express"}, // 后端框架
	}

	// 🛡️ 特殊规则：跨领域主题冲突检测
	programmingLanguages := []string{"go", "python", "java", "javascript", "cpp", "csharp", "rust", "php", "ruby"}
	databases := []string{"mysql", "postgresql", "mongodb", "redis", "sqlite", "oracle"}
	frameworks := []string{"react", "vue", "angular", "django", "flask", "spring", "express"}

	// AI主题与编程语言冲突
	if topic1 == "ai" {
		for _, lang := range programmingLanguages {
			if topic2 == lang {
				return true
			}
		}
	}
	if topic2 == "ai" {
		for _, lang := range programmingLanguages {
			if topic1 == lang {
				return true
			}
		}
	}

	// 部署主题与编程语言冲突
	if topic1 == "deployment" {
		for _, lang := range programmingLanguages {
			if topic2 == lang {
				return true
			}
		}
		for _, db := range databases {
			if topic2 == db {
				return true
			}
		}
		for _, fw := range frameworks {
			if topic2 == fw {
				return true
			}
		}
	}
	if topic2 == "deployment" {
		for _, lang := range programmingLanguages {
			if topic1 == lang {
				return true
			}
		}
		for _, db := range databases {
			if topic1 == db {
				return true
			}
		}
		for _, fw := range frameworks {
			if topic1 == fw {
				return true
			}
		}
	}

	for _, group := range conflictGroups {
		found1, found2 := false, false
		for _, item := range group {
			if topic1 == item {
				found1 = true
			}
			if topic2 == item {
				found2 = true
			}
		}
		// 如果两个主题在同一组但不相同，则冲突
		if found1 && found2 && topic1 != topic2 {
			return true
		}
	}

	return false
}

// areTopicsRelated 检查两个主题是否相关
func (kl *KnowledgeLearner) areTopicsRelated(topic1, topic2 string) bool {
	// 定义相关的主题组
	relatedGroups := [][]string{
		{"go", "deployment", "docker", "kubernetes"},        // Go语言相关技术栈
		{"python", "ai", "django", "flask"},                 // Python相关技术栈
		{"java", "spring", "deployment"},                    // Java相关技术栈
		{"javascript", "react", "vue", "angular", "nodejs"}, // JavaScript相关技术栈
		{"mysql", "database", "deployment"},                 // 数据库相关
		{"ai", "python", "deployment"},                      // AI相关技术栈
	}

	for _, group := range relatedGroups {
		found1, found2 := false, false
		for _, item := range group {
			if topic1 == item {
				found1 = true
			}
			if topic2 == item {
				found2 = true
			}
		}
		if found1 && found2 {
			return true
		}
	}

	return false
}

// filterTechnicalTerms 过滤出真正的技术词汇
func (kl *KnowledgeLearner) filterTechnicalTerms(terms []string) []string {
	// 定义核心技术词汇（编程语言、框架等）
	coreTechTerms := map[string]bool{
		"go语言": true, "golang": true, "python": true, "java": true, "javascript": true,
		"c++": true, "c#": true, "rust": true, "php": true, "ruby": true,
		"react": true, "vue": true, "angular": true, "nodejs": true,
		"mysql": true, "postgresql": true, "mongodb": true, "redis": true,
		"docker": true, "kubernetes": true, "localai": true,
	}

	var filtered []string
	for _, term := range terms {
		if coreTechTerms[term] {
			filtered = append(filtered, term)
		}
	}
	return filtered
}

// calculateTechnicalConflictPenalty 计算技术词汇冲突惩罚
func (kl *KnowledgeLearner) calculateTechnicalConflictPenalty(queryTerms, questionTerms, answerTerms []string) float64 {
	// 定义冲突的技术组合（不同编程语言之间）
	conflictGroups := [][]string{
		{"go语言", "golang", "python", "java", "javascript", "c++", "c#", "rust", "php", "ruby"},
		{"react", "vue", "angular"},
		{"mysql", "postgresql", "mongodb"},
	}

	penalty := 0.0

	// 检查查询和问题/答案之间是否有冲突的技术词汇
	for _, queryTerm := range queryTerms {
		for _, group := range conflictGroups {
			if kl.containsString(group, queryTerm) {
				// 检查问题和答案中是否有同组的其他技术
				for _, questionTerm := range questionTerms {
					if kl.containsString(group, questionTerm) && queryTerm != questionTerm {
						penalty += 0.8 // 重大冲突惩罚
						break
					}
				}
				for _, answerTerm := range answerTerms {
					if kl.containsString(group, answerTerm) && queryTerm != answerTerm {
						penalty += 0.6 // 答案冲突惩罚
						break
					}
				}
			}
		}
	}

	return penalty
}

// containsString 检查字符串切片是否包含指定字符串
func (kl *KnowledgeLearner) containsString(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}
